import React, { useEffect, useState } from 'react';
import Button from '../../../../components/Field/Button';
import { useDispatch, useSelector } from 'react-redux';
import PopUp from '../../../../components/PopUp/PopUp';
import SearchableDropdown from '../../../../components/Field/SearchableDropdown';
import Input from '../../../../components/Field/Input';
import {
  setAllContentMappingData,
  setSubTopicData,
  setTopicData,
  useCreateContentServiceMutation,
  useLazyGetAllContentMappingServiceQuery,
  useLazyGetSubTopicByIdsServiceQuery,
  useLazyGetTopicByIdsServiceQuery,
  useStoreContentInS3ServiceMutation
} from './processSelector.slice';
import Toastify from '../../../../components/PopUp/Toastify';
import Table from '../../../../components/Layout/Table';
import { ContentMappingHeader } from './TableHeaderData';

const ContentMapping = () => {
  const [popUp, setPopUp] = useState(false);

  const [selectedCourse, setSelectedCourse] = useState({ id: '', name: '' });
  const [selectedSubject, setSelectedSubject] = useState({ id: '', name: '' });
  const [selectedTopic, setSelectedTopic] = useState({ id: '', name: '' });
  const [selectedSubTopic, setSelectedSubTopic] = useState({ id: '', name: '' });
  const [language, setLanguage] = useState({ id: '', name: '' });
  const [url, setUrl] = useState('');
  const [file, setFile] = useState(null);
  const [res, setRes] = useState(null);

  const [getSubTopicDataService] = useLazyGetSubTopicByIdsServiceQuery();
  const [getTopicDataService] = useLazyGetTopicByIdsServiceQuery();
  const [createContentService] = useCreateContentServiceMutation();
  const [storeContentInS3Service] = useStoreContentInS3ServiceMutation();
  const [getAllContentMappingService] = useLazyGetAllContentMappingServiceQuery();

  const courseData = useSelector((state) => state.processSelector.courseData);
  const subjectData = useSelector((state) => state.processSelector.subjectData);
  const topicData = useSelector((state) => state.processSelector.topicsData);
  const subTopicData = useSelector((state) => state.processSelector.subTopicData);
  const allContentMappingData = useSelector((state) => state.processSelector.allContentMappingData);
  const dispatch = useDispatch();

  const languages = [
    { id: '0', name: 'English' },
    { id: '1', name: 'Tamil' },
    { id: '2', name: 'Telugu' },
    { id: '3', name: 'Kannada' },
    { id: '4', name: 'Malayalam' },
    { id: '5', name: 'Hindi' }
  ];

  useEffect(() => {
    fetchAllContentMappingService();
  }, []);

  const fetchAllContentMappingService = async () => {
    try {
      const res = await getAllContentMappingService().unwrap();
      dispatch(setAllContentMappingData(res));
    } catch (error) {
      setRes(error);
    }
  };

  useEffect(() => {
    const fetchTopicsByIdsService = async () => {
      try {
        const res = await getSubTopicDataService({
          course_id: selectedCourse.id,
          subject_id: selectedSubject.id,
          topic_id: selectedTopic.id
        }).unwrap();

        dispatch(setSubTopicData(res));
      } catch (error) {
        setRes(error);
      }
    };

    if (selectedCourse?.id && selectedSubject?.id && selectedTopic?.id) {
      fetchTopicsByIdsService();
    }
  }, [selectedCourse, selectedSubject, selectedTopic, getSubTopicDataService, dispatch]);

  useEffect(() => {
    const fetchTopicsByIdsService = async () => {
      try {
        const res = await getTopicDataService({
          course_id: selectedCourse.id,
          subject_id: selectedSubject.id
        }).unwrap();

        dispatch(setTopicData(res));
      } catch (error) {
        setRes(error);
      }
    };

    if (selectedCourse?.id && selectedSubject?.id) {
      fetchTopicsByIdsService();
    }
  }, [selectedCourse, selectedSubject, getTopicDataService, dispatch]);

  const handleCreateContent = async () => {
    try {
      const res = await createContentService({
        course_id: selectedCourse.id,
        course_name: selectedCourse.name,
        subject_id: selectedSubject.id,
        subject_name: selectedSubject.name,
        topic_id: selectedTopic.id,
        topic_name: selectedTopic.name,
        sub_topic_id: selectedSubTopic.id,
        sub_topic_name: selectedSubTopic.name,
        process_selector_url: url,
        language: language.name
      }).unwrap();
      // dispatch(setAllContentMappingData((prev) => [...prev, res]));
      fetchAllContentMappingService();
      setRes(res);
    } catch (error) {
      setRes(error);
    } finally {
      setPopUp(false);
      setSelectedCourse({ id: '', name: '' });
      setSelectedSubject({ id: '', name: '' });
      setSelectedTopic({ id: '', name: '' });
      setSelectedSubTopic({ id: '', name: '' });
      setUrl('');
      setFile(null);
    }
  };

  const handleFileChange = async (e) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);

      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('subject', selectedSubject.name);

      try {
        const res = await storeContentInS3Service(formData).unwrap();

        setUrl(res.fileUrl);
        setRes(res);
      } catch (err) {
        setRes(err);
      }
    }
  };

  return (
    <div>
      <Toastify res={res} resClear={() => setRes(null)} />
      {popUp && (
        <PopUp
          title={'Create Content Mapping'}
          onClose={() => {
            setPopUp(false);
            setSelectedCourse({ id: '', name: '' });
            setSelectedSubject({ id: '', name: '' });
            setSelectedTopic({ id: '', name: '' });
            setSelectedSubTopic({ id: '', name: '' });
            setUrl('');
            setFile(null);
          }}
          post={handleCreateContent}
          width="lg">
          <div className="grid grid-cols-2 gap-2 mb-4">
            <SearchableDropdown
              label="Course Name"
              value={selectedCourse?.id}
              placeholder="Select/Search the Course Name"
              options={courseData}
              onChange={(e) => {
                setSelectedCourse(e);
                setSelectedSubject({ id: '', name: '' });
                setSelectedTopic({ id: '', name: '' });
              }}
              required
            />

            <SearchableDropdown
              label="Subject Name"
              value={selectedSubject?.id}
              placeholder="Select/Search the Subject Name"
              options={subjectData}
              onChange={(e) => {
                setSelectedSubject(e);
                setSelectedTopic({ id: '', name: '' });
              }}
              disabled={!selectedCourse?.id}
              required
            />

            <SearchableDropdown
              label="Topic Name"
              value={selectedTopic?.id}
              placeholder="Select/Search the Topic Name"
              options={topicData}
              onChange={(e) => setSelectedTopic(e)}
              disabled={!selectedCourse?.id || !selectedSubject?.id}
              required
            />
            <SearchableDropdown
              label="Sub Topic Name"
              value={selectedSubTopic?.id}
              placeholder="Select/Search the Topic Name"
              options={subTopicData}
              onChange={(e) => setSelectedSubTopic(e)}
              disabled={!selectedCourse?.id || !selectedSubject?.id}
              required
            />
            <Input
              type="file"
              name="document"
              label="Upload File"
              accept=".pdf,.jpg,.png"
              required
              placeholder="Select a file"
              onChange={handleFileChange}
              error={false}
            />
            <SearchableDropdown
              label="Languages"
              value={language.id}
              placeholder="Select/Search the Language"
              options={languages}
              onChange={(e) => setLanguage(e)}
              required
            />
          </div>

          <Input
            label="Content Url"
            value={url}
            // onChange={(e) => setUrl(e.target.value)}
            className="border rounded"
            disabled
            required
          />
        </PopUp>
      )}
      {/* <section className="flex justify-between items-center">
        <div></div>
        <div>
          <Button name={'Content Mapping'} className={''} onClick={() => setPopUp(true)} />
        </div>
      </section> */}
      <Table
        title="Content Mapping"
        onAddNew={() => setPopUp(true)}
        buttonName="Create Content Mapping"
        header={ContentMappingHeader}
        data={allContentMappingData}
        searchBy={['course_name', 'subject_name', 'topic_name', 'sub_topic_name', 'language']}
        searchPlaceholder="Search by Course, Subject, Topic, Sub Topic or Language Name"
      />
    </div>
  );
};

export default ContentMapping;
