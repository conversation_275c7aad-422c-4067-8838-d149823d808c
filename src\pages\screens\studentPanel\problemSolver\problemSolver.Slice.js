import { createSlice } from '@reduxjs/toolkit';
import { DoubtSolverApi } from '../../../../redux/api/api';

const initialState = {
  doubtSolverData: null
};

export const doubtSolverApiSlice = DoubtSolverApi.injectEndpoints({
  endpoints: (builder) => ({
    doubtSolverService: builder.mutation({
      query: (formData) => {
        // Accept FormData directly
        return {
          url: '/doubt_solver',
          method: 'POST',
          body: formData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('Doubt Solver Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['DoubtSolver']
    }),
    feedbackDoubtSolverService: builder.mutation({
      query: ({ user_id, satisfied }) => ({
        url: '/feedback_doubt_solver',
        method: 'POST',
        body: { user_id, satisfied },
        headers: { 'Content-Type': 'application/json' },
        responseHandler: async (res) => res.json()
      }),
      transformResponse: (response) => {
        console.log('Feedback Doubt Solver Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['FeedbackDoubtSolver']
    }),
    youtubeSearchService: builder.mutation({
      query: (formData) => {
        // Accept FormData for text, audio, image, and user_id
        return {
          url: '/youtube_search',
          method: 'POST',
          body: formData,
          responseHandler: async (res) => res.json()
        };
      },
      transformResponse: (response) => {
        console.log('YouTube Search Response:', response);
        return response;
      },
      transformErrorResponse: ({ originalStatus, status, data }) => ({
        status: originalStatus ?? status,
        data
      }),
      providesTags: ['YouTubeSearch']
    })
  })
});

const doubtSolverSlice = createSlice({
  name: 'doubtSolver',
  initialState,
  reducers: {
    setDoubtSolverData: (state, action) => {
      state.doubtSolverData = action.payload;
    }
  }
});

export default doubtSolverSlice.reducer;
export const { setDoubtSolverData } = doubtSolverSlice.actions;
export const { useDoubtSolverServiceMutation, useFeedbackDoubtSolverServiceMutation,useYoutubeSearchServiceMutation } =
  doubtSolverApiSlice;
