import React, { useMemo, useEffect } from 'react';
import { useLocation, <PERSON> } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  BookCheck,
  CircleSlash2,
  CircleUser,
  ClipboardList,
  Edit,
  Calendar,
  MapPin,
  LucideLayoutDashboard,
  MapIcon,
  FileCheck,
  CheckCircle,
  FileText,
  Subtitles,
  CalendarHeart,
  List,
  School,
  Users,
  LineChart,
  Settings,
  BookOpenCheck,
  Video,
  UserPlus,
  UserCheck,
  Save,
  Info,
  ChartSpline,
  ListCheck
} from 'lucide-react';
import { MdAllInbox, MdOutlineLiveTv } from 'react-icons/md';
import { CiMemoPad } from 'react-icons/ci';
import { GoVideo } from 'react-icons/go';
import { BsLightningCharge } from 'react-icons/bs';
import { RiUserCommunityFill } from 'react-icons/ri';
import { FaChalkboardTeacher } from 'react-icons/fa';
import { BiChalkboard } from 'react-icons/bi';
import { GiTeacher } from 'react-icons/gi';
import { HiOutlineViewGrid } from "react-icons/hi";


import { motion, AnimatePresence } from 'framer-motion';

import { setMenuData, useLazyGetRoleBasedMenuServiceQuery } from './navbar.slice';

const SideBar = ({ isOpen, onLinkClick }) => {
  const [getMenu] = useLazyGetRoleBasedMenuServiceQuery();
  const dispatch = useDispatch();
  const menus = useSelector((state) => state.menu.menuData);
  const location = useLocation();
  const role = sessionStorage.role;

  useEffect(() => {
    if (!role) return;
    const fetchMenu = async () => {
      try {
        const res = await getMenu(role).unwrap();
        dispatch(setMenuData(res));
      } catch (err) {
        console.error('Failed to fetch menu:', err);
      }
    };
    fetchMenu();
  }, [dispatch, getMenu, role]);

  const roleColorMap = {
    director: 'bg-director',
    student: 'bg-student',
    center_counselor: 'bg-counselor',
    kota_teacher: 'bg-teacher',
    faculty: 'bg-trainee',
    parent: 'bg-parents',
    mendor: 'bg-mentor'
  };

  const bgClass = roleColorMap[sessionStorage.role] || 'bg-white';

  const iconMap = useMemo(
    () => ({
      Overview: <HiOutlineViewGrid size={20} />,
      'Faculty Register': <CircleUser size={20} />,
      'Students Info': <Subtitles size={20} />, 
      'E Book Center': <BookCheck />,
      'Create Your Own Center': <School size={20} />,
      'Create Your Own Test': <CiMemoPad size={20} />,
      'Mock Test Simulation': <CiMemoPad size={20} />,
      'Recorded Video': <GoVideo size={20} />,
      'Booster Module': <BsLightningCharge size={20} />,
      'Student Community': <RiUserCommunityFill size={20} />,
      'AI - Tutor': <FaChalkboardTeacher size={20} />,
      Events: <CalendarHeart size={20} />,
      'Add Teachers': <Users size={20} />,
      Batches: <GiTeacher size={20} />,
      Courses: <GiTeacher size={20} />,
      'Add Center': <School size={20} />,
    
      'List Teachers': <List size={20} />,
      'Process Selector': <Settings size={20} />,
      Subjects: <BookOpenCheck size={20} />,
      Mapping: <MapIcon size={20} />,
      Dependencies: <CircleSlash2 size={20} />,
      'Live Streaming': <MdOutlineLiveTv size={20} />,
      Inbox: <MdAllInbox size={20} />,
      'Learn Practically': <BiChalkboard size={20} />,
      'Mapping Centers': <MapPin size={20} />,
      'Schedule Events': <Calendar size={20} />,
      'Live Viewer': <Video size={20} />,
      'Create Test': <FileText size={20} />,
      'Add Students': <UserPlus size={20} />,
      'Add Faculty': <UserCheck size={20} />,
      'List Students': <Users size={20} />,
      'List Centers': <ListCheck size={20} />,
      'List Faculty': <Edit size={20} />,
      'Student Info': <CircleUser size={20} />,
      'Center Info': <CircleUser size={20} />,
      'Attendance': <Calendar size={20} />,
      'Overall Performance': <LineChart size={20} />,
      'Paper Based Evaluator': <FileCheck size={20} />,
      'Evaluator Result': <CheckCircle size={20} />,
      'Paper Based Test': <FileText size={20} />,
      'PBE Result': <FileText size={20} />,
      'Problem Solver': <FileText size={20} />,
      Dashboard: <ChartSpline size={20} />,
      Recommendation: <BiChalkboard size={20} />,
      'Question Generator': <FileText size={20} />,
      Evaluator: <Save size={20} />,
      Default: <UserCheck size={18} />
    }),
    []
  );

  return (
    <aside
      className={`${bgClass} h-full text-white fixed inset-y-0 left-0 z-40 w-64 transition-transform duration-300 ease-in-out md:relative md:inset-auto md:z-auto md:transition-all ${isOpen ? 'translate-x-0 md:w-60' : '-translate-x-full md:w-0'}`}>
      <div className="flex flex-col h-full">
        <nav className="flex-1 overflow-y-auto overflow-x-hidden mx-1">
          
          <ul className="flex flex-col gap-1">
            {menus?.length > 0 ? (
              menus.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <li key={item.href || item.name}>
                    <Link
                      to={item.href}
                      onClick={onLinkClick}
                      className={`
                        relative flex items-center p-2 rounded-lg
                        transition-colors duration-200 group
                        ${
                          isActive
                            ? 'bg-black/30 my-1 text-white font-semibold'
                            : 'text-white hover:bg-white/10 hover:text-white'
                        }
                      `}
                      title={!isOpen ? item.name : ''}>
                      {/* Active Link Indicator */}
                      <AnimatePresence>
                        {isActive && (
                          <motion.div
                            layoutId="active-indicator"
                            className={`absolute left-0 top-2 bottom-2 w-1 ${bgClass} rounded-r-full`}
                          />
                        )}
                      </AnimatePresence>

                      <div className="flex-shrink-0">{iconMap[item.name] || iconMap.Default}</div>

                      {/* Text Label with Animation */}
                      <AnimatePresence>
                        {isOpen && (
                          <motion.span
                            initial={{ opacity: 0, width: 0, marginLeft: 0 }}
                            animate={{ opacity: 1, width: 'auto', marginLeft: '0.75rem' }}
                            exit={{
                              opacity: 0,
                              width: 0,
                              marginLeft: 0,
                              transition: { duration: 0.1 }
                            }}
                            transition={{ duration: 0.2, ease: 'easeInOut' }}
                            className="whitespace-nowrap overflow-hidden">
                            {item.name}
                          </motion.span>
                        )}
                      </AnimatePresence>
                    </Link>
                  </li>
                );
              })
            ) : (
              <div className="text-center text-gray-400 text-sm italic p-4">
                Failed to load menu.
              </div>
            )}
          </ul>
        </nav>
      </div>
    </aside>
  );
};

export default SideBar;
