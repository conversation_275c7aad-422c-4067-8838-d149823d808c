'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useListBatchStudentsQuery,
  useGetStudentEvaluationsQuery
} from './evaluationResponse.Slice';
import {
  FaUsers,
  FaGraduationCap,
  FaCalendarPlus,
  FaUser,
  FaChartLine,
  FaEyeSlash,
  FaChartBar,
  FaTimes,
  FaExclamationTriangle,
  FaCheck,
  FaLightbulb,
  FaCheckCircle,
  FaUserEdit,
  FaThumbsUp,
  FaSearch,
  FaArrowUp,
  FaClipboardList,
  FaStar,
  FaAward,
  FaBookOpen,
  FaChevronRight,
  FaTrophy,
  FaPercentage,
  FaChartPie
} from 'react-icons/fa';
import { BiSolidCalendarEdit } from 'react-icons/bi';

const StudentList = () => {
  const { data: batches, isLoading, isError, error } = useListBatchStudentsQuery();
  const [selectedStudentId, setSelectedStudentId] = useState(null);
  const [selectedTestId, setSelectedTestId] = useState(null);
  const [expandedBatches, setExpandedBatches] = useState({});
  const feedbackRef = useRef(null);

  const {
    data: evaluations,
    isLoading: isEvalLoading,
    isFetching,
    isError: isEvalError,
    error: evalError
  } = useGetStudentEvaluationsQuery(selectedStudentId, { skip: !selectedStudentId });

  // Calculate overall score for a student
  const calculateOverallScore = (studentEvaluations) => {
    if (!studentEvaluations || studentEvaluations.length === 0) return null;

    let totalQuestions = 0;
    let totalCorrect = 0;
    let totalMarks = 0;
    let totalPossibleMarks = 0;

    studentEvaluations.forEach((evaluation) => {
      if (evaluation.performance_analysis) {
        evaluation.performance_analysis.forEach((question) => {
          totalQuestions++;
          if (question.is_correct) {
            totalCorrect++;
          }
          if (question.marks_awarded !== null && question.marks_awarded !== undefined) {
            totalMarks += question.marks_awarded;
          }
          // Assuming each question has a maximum of 1 mark if not specified
          totalPossibleMarks += 1;
        });
      }
    });

    const percentage = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;
    const marksPercentage =
      totalPossibleMarks > 0 ? Math.round((totalMarks / totalPossibleMarks) * 100) : 0;

    return {
      percentage,
      marksPercentage,
      totalCorrect,
      totalQuestions,
      totalMarks,
      totalPossibleMarks,
      testsCompleted: studentEvaluations.length
    };
  };

  // Calculate score for individual test
  const calculateTestScore = (evaluation) => {
    if (!evaluation.performance_analysis) return null;

    const totalQuestions = evaluation.performance_analysis.length;
    let totalCorrect = 0;
    let totalMarks = 0;

    evaluation.performance_analysis.forEach((question) => {
      if (question.is_correct) {
        totalCorrect++;
      }
      if (question.marks_awarded !== null && question.marks_awarded !== undefined) {
        totalMarks += question.marks_awarded;
      }
    });

    const percentage = totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0;

    return {
      percentage,
      totalCorrect,
      totalQuestions,
      totalMarks
    };
  };

  // Get performance level and color
  const getPerformanceLevel = (percentage) => {
    if (percentage >= 80)
      return {
        level: 'Excellent',
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200'
      };
    if (percentage >= 60)
      return {
        level: 'Good',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200'
      };
    if (percentage >= 40)
      return {
        level: 'Average',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200'
      };
    return {
      level: 'Needs Improvement',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    };
  };

  const handleResultClick = useCallback(
    (studentId) => {
      console.log('handleResultClick called with studentId:', studentId);
      if (studentId === selectedStudentId) {
        console.log('Closing modal, resetting states');
        setSelectedStudentId(null);
        setSelectedTestId(null);
      } else {
        console.log('Opening modal for studentId:', studentId);
        setSelectedStudentId(studentId);
        setSelectedTestId(null);
      }
    },
    [selectedStudentId]
  );

  const closeModal = () => {
    console.log('Closing modal');
    setSelectedStudentId(null);
    setSelectedTestId(null);
  };

  const toggleBatchExpansion = (batchId) => {
    setExpandedBatches((prev) => ({
      ...prev,
      [batchId]: !prev[batchId]
    }));
  };

  useEffect(() => {
    if (selectedTestId && feedbackRef.current) {
      feedbackRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, [selectedTestId]);

  // Enhanced Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.15,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const modalVariants = {
    hidden: {
      opacity: 0,
      scale: 0.8,
      y: 50
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.8,
      y: 50,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const cardHoverVariants = {
    rest: { scale: 1, y: 0 },
    hover: {
      scale: 1.02,
      y: -5,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const buttonVariants = {
    rest: { scale: 1 },
    hover: { scale: 1.05 },
    tap: { scale: 0.95 }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="flex flex-col items-center space-y-6 p-8 bg-white rounded-3xl shadow-xl border border-gray-100">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Number.POSITIVE_INFINITY, ease: 'linear' }}
            className="relative">
            <div className="w-16 h-16 border-4 border-[#F59E0B]/20 rounded-full"></div>
            <div className="absolute top-0 left-0 w-16 h-16 border-4 border-[#F59E0B] border-t-transparent rounded-full"></div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-center">
            <p className="text-xl text-gray-700 font-semibold mb-2">Loading Students</p>
            <p className="text-gray-500">Please wait while we fetch the data...</p>
          </motion.div>
        </motion.div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          className="text-center p-10 bg-white border-2 border-red-200 rounded-3xl shadow-xl max-w-md">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}>
            <FaExclamationTriangle className="text-6xl text-red-500 mb-6 mx-auto" />
          </motion.div>
          <h3 className="text-2xl font-bold text-gray-800 mb-4">Oops! Something went wrong</h3>
          <p className="text-lg text-red-600 font-medium">
            {error?.data?.message || 'Failed to load students'}
          </p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="mt-6 px-6 py-3 bg-[#F59E0B] text-white rounded-xl font-semibold hover:bg-[#E5890A] transition-colors duration-300"
            onClick={() => window.location.reload()}>
            Try Again
          </motion.button>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="container mx-auto max-w-7xl">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }}
          className="text-center mb-16">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            className="inline-flex items-center justify-center w-20 h-20 bg-[#F59E0B] rounded-3xl mb-6 shadow-lg">
            <FaUsers className="text-white text-3xl" />
          </motion.div>
          <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-4">Student Batches</h1>
          <p className="text-xl text-gray-600 font-light max-w-2xl mx-auto leading-relaxed">
            Comprehensive evaluation management system for tracking student performance across all
            batches
          </p>
          <motion.div
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="w-24 h-1 bg-[#F59E0B] mx-auto mt-6 rounded-full"></motion.div>
        </motion.div>

        {/* Enhanced Batches List */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-10">
          {batches?.batches?.map((batch, batchIndex) => (
            <motion.div
              key={batch.batch_id}
              variants={itemVariants}
              className="bg-white border border-gray-200 rounded-3xl shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden">
              {/* Enhanced Batch Header */}
              <div className="p-8 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      className="w-16 h-16 rounded-2xl bg-[#F59E0B] flex items-center justify-center mr-6 shadow-lg">
                      <FaGraduationCap className="text-white text-2xl" />
                    </motion.div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-800 mb-2">{batch.batch_name}</h2>
                      <p className="text-gray-600 text-lg">{batch.description}</p>
                    </div>
                  </div>
                  <motion.button
                    onClick={() => toggleBatchExpansion(batch.batch_id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-12 h-12 rounded-xl border-2 border-[#F59E0B] flex items-center justify-center text-[#F59E0B] hover:bg-[#F59E0B] hover:text-white transition-all duration-300">
                    <motion.div
                      animate={{ rotate: expandedBatches[batch.batch_id] ? 90 : 0 }}
                      transition={{ duration: 0.3 }}>
                      <FaChevronRight />
                    </motion.div>
                  </motion.button>
                </div>

                <div className="flex items-center space-x-8 text-sm text-gray-500">
                  <motion.span
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center bg-white px-4 py-2 rounded-xl border border-gray-200">
                    <FaCalendarPlus className="text-[#F59E0B] mr-2" />
                    Created: {new Date(batch.created_at).toLocaleDateString()}
                  </motion.span>
                  <motion.span
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center bg-white px-4 py-2 rounded-xl border border-gray-200">
                    <BiSolidCalendarEdit className="text-[#F59E0B] mr-2" />
                    Updated: {new Date(batch.updated_at).toLocaleDateString()}
                  </motion.span>
                  <motion.span
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center bg-[#F59E0B] text-white px-4 py-2 rounded-xl">
                    <FaUsers className="mr-2" />
                    {batch.students.length} Students
                  </motion.span>
                </div>
              </div>

              {/* Enhanced Students Grid - NO SCORE DISPLAY */}
              <AnimatePresence>
                {expandedBatches[batch.batch_id] !== false && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
                    className="overflow-hidden">
                    <div className="p-8 grid gap-6">
                      {batch.students.map((student, index) => (
                        <motion.div
                          key={student.student_id}
                          initial={{ opacity: 0, x: -30 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          variants={cardHoverVariants}
                          whileHover="hover"
                          initial="rest"
                          className="group bg-white border-2 border-gray-200 rounded-2xl p-6 hover:border-[#F59E0B]/50 hover:shadow-lg transition-all duration-300 cursor-pointer">
                          <div className="flex justify-between items-center">
                            <div className="flex items-center">
                              <motion.div
                                whileHover={{ scale: 1.1, rotate: 5 }}
                                className="relative">
                                <div className="w-14 h-14 rounded-2xl bg-gray-100 border-2 border-gray-300 flex items-center justify-center mr-6 group-hover:border-[#F59E0B] group-hover:bg-[#F59E0B]/10 transition-all duration-300">
                                  <FaUser className="text-gray-500 group-hover:text-[#F59E0B] transition-colors duration-300 text-xl" />
                                </div>
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="absolute -top-1 -right-1 w-6 h-6 bg-[#F59E0B] rounded-full flex items-center justify-center">
                                  <FaStar className="text-white text-xs" />
                                </motion.div>
                              </motion.div>
                              <div>
                                <h3 className="text-xl font-bold text-gray-800 mb-1 group-hover:text-[#F59E0B] transition-colors duration-300">
                                  {student.first_name}
                                </h3>
                                <p className="text-sm text-gray-500 mb-2">
                                  Student ID: {student.student_id}
                                </p>
                                <div className="flex items-center space-x-2">
                                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-lg text-xs font-medium">
                                    Active
                                  </span>
                                  <span className="px-2 py-1 bg-[#F59E0B]/10 text-[#F59E0B] rounded-lg text-xs font-medium">
                                    Batch {batchIndex + 1}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <motion.button
                              onClick={() => handleResultClick(student.student_id)}
                              variants={buttonVariants}
                              whileHover="hover"
                              whileTap="tap"
                              disabled={isEvalLoading || isFetching}
                              className={`relative px-8 py-4 border-2 border-[#F59E0B] text-[#F59E0B] rounded-2xl hover:text-white hover:bg-[#F59E0B] transition-all duration-300 font-semibold flex items-center space-x-3 shadow-lg hover:shadow-xl ${
                                isEvalLoading || isFetching ? 'opacity-50 cursor-not-allowed' : ''
                              }`}>
                              {isEvalLoading || isFetching ? (
                                <motion.div
                                  animate={{ rotate: 360 }}
                                  transition={{
                                    duration: 1,
                                    repeat: Number.POSITIVE_INFINITY,
                                    ease: 'linear'
                                  }}
                                  className="w-5 h-5 border-2 border-current border-t-transparent rounded-full"></motion.div>
                              ) : (
                                <>
                                  <motion.div
                                    animate={{
                                      rotate: selectedStudentId === student.student_id ? 180 : 0
                                    }}
                                    transition={{ duration: 0.3 }}>
                                    {selectedStudentId === student.student_id ? (
                                      <FaEyeSlash />
                                    ) : (
                                      <FaChartLine />
                                    )}
                                  </motion.div>
                                  <span>
                                    {selectedStudentId === student.student_id
                                      ? 'Hide Results'
                                      : 'View Results'}
                                  </span>
                                </>
                              )}
                            </motion.button>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Modal */}
        <AnimatePresence>
          {selectedStudentId && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-md flex justify-center items-center z-50 p-4"
              onClick={closeModal}>
              <motion.div
                variants={modalVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                onClick={(e) => e.stopPropagation()}
                className="bg-white border border-gray-200 rounded-3xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                {/* Enhanced Modal Header */}
                <div className="flex justify-between items-center p-8 border-b border-gray-100 bg-gray-50">
                  <div className="flex items-center">
                    <motion.div
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                      className="w-16 h-16 rounded-2xl bg-[#F59E0B] flex items-center justify-center mr-6 shadow-lg">
                      <FaChartBar className="text-white text-2xl" />
                    </motion.div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-800 mb-2">
                        Evaluation Dashboard
                      </h2>
                      <p className="text-gray-600">
                        Comprehensive performance analysis for Student ID:
                        <span className="font-semibold text-[#F59E0B] ml-2">
                          {selectedStudentId}
                        </span>
                      </p>
                    </div>
                  </div>
                  <motion.button
                    onClick={closeModal}
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    className="w-12 h-12 rounded-2xl bg-white border-2 border-gray-300 flex items-center justify-center text-gray-500 hover:border-red-400 hover:text-red-500 hover:bg-red-50 transition-all duration-300 shadow-lg">
                    <FaTimes className="text-lg" />
                  </motion.button>
                </div>

                {/* Enhanced Modal Content */}
                <div className="flex-1 overflow-y-auto p-8">
                  {isEvalLoading || isFetching ? (
                    <div className="text-center py-16">
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{
                          duration: 2,
                          repeat: Number.POSITIVE_INFINITY,
                          ease: 'linear'
                        }}
                        className="relative mx-auto mb-8">
                        <div className="w-20 h-20 border-4 border-[#F59E0B]/20 rounded-full"></div>
                        <div className="absolute top-0 left-0 w-20 h-20 border-4 border-[#F59E0B] border-t-transparent rounded-full"></div>
                      </motion.div>
                      <p className="text-xl text-gray-600 font-semibold">Loading evaluations...</p>
                      <p className="text-gray-500 mt-2">
                        Please wait while we fetch the detailed analysis
                      </p>
                    </div>
                  ) : isEvalError || !evaluations?.batches ? (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-center py-16">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}>
                        <FaClipboardList className="text-8xl text-gray-300 mb-8 mx-auto" />
                      </motion.div>
                      <h3 className="text-2xl font-bold text-gray-700 mb-4">
                        No Tests Attended Yet
                      </h3>
                      <p className="text-xl text-gray-600 font-medium mb-2">
                        This student hasn't attended any tests
                      </p>
                      <p className="text-gray-500 max-w-md mx-auto">
                        Test evaluations and performance analytics will appear here once the student
                        completes their first assessment
                      </p>
                    </motion.div>
                  ) : (
                    (() => {
                      const selectedBatch = evaluations.batches.find((b) =>
                        b.students.some((s) => s.student_id === selectedStudentId)
                      );
                      const selectedStudent = selectedBatch?.students.find(
                        (s) => s.student_id === selectedStudentId
                      );
                      const overallScore = calculateOverallScore(selectedStudent?.evaluations);
                      const performanceLevel = overallScore
                        ? getPerformanceLevel(overallScore.percentage)
                        : null;

                      return selectedStudent?.evaluations?.length > 0 ? (
                        <div className="space-y-8">
                          {/* Overall Score Dashboard - ONLY SHOWN IN MODAL */}
                          {overallScore && (
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="bg-white border-2 border-gray-200 rounded-2xl p-8 shadow-lg">
                              <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                <FaTrophy className="text-[#F59E0B] mr-3" />
                                Overall Performance Score
                              </h3>

                              <div className="grid md:grid-cols-4 gap-6">
                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  className={`p-6 rounded-2xl border-2 ${performanceLevel.bgColor} ${performanceLevel.borderColor} text-center`}>
                                  <FaPercentage
                                    className={`text-4xl ${performanceLevel.color} mb-3 mx-auto`}
                                  />
                                  <div
                                    className={`text-3xl font-bold ${performanceLevel.color} mb-2`}>
                                    {overallScore.percentage}%
                                  </div>
                                  <div className="text-sm text-gray-600">Overall Score</div>
                                  <div
                                    className={`text-xs font-medium mt-1 ${performanceLevel.color}`}>
                                    {performanceLevel.level}
                                  </div>
                                </motion.div>

                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  className="p-6 bg-blue-50 border-2 border-blue-200 rounded-2xl text-center">
                                  <FaCheckCircle className="text-4xl text-blue-500 mb-3 mx-auto" />
                                  <div className="text-3xl font-bold text-blue-600 mb-2">
                                    {overallScore.totalCorrect}
                                  </div>
                                  <div className="text-sm text-gray-600">Correct Answers</div>
                                  <div className="text-xs text-blue-600 mt-1">
                                    out of {overallScore.totalQuestions}
                                  </div>
                                </motion.div>

                                <motion.div
                                  whileHover={{ scale: 1.05 }}
                                  className="p-6 bg-[#F59E0B]/10 border-2 border-[#F59E0B]/30 rounded-2xl text-center">
                                  <FaBookOpen className="text-4xl text-[#F59E0B] mb-3 mx-auto" />
                                  <div className="text-3xl font-bold text-[#F59E0B] mb-2">
                                    {overallScore.testsCompleted}
                                  </div>
                                  <div className="text-sm text-gray-600">Tests Completed</div>
                                  <div className="text-xs text-[#F59E0B] mt-1">
                                    Total Assessments
                                  </div>
                                </motion.div>
                              </div>
                            </motion.div>
                          )}

                          {/* Enhanced Test Overview */}
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="bg-white border-2 border-gray-200 rounded-2xl p-8 shadow-lg">
                            <div className="flex items-center justify-between mb-6">
                              <h3 className="text-2xl font-bold text-gray-800 flex items-center">
                                <FaAward className="text-[#F59E0B] mr-3" />
                                Individual Test Performance
                              </h3>
                              <div className="flex items-center space-x-4">
                                <span className="px-4 py-2 bg-[#F59E0B]/10 text-[#F59E0B] rounded-xl font-semibold">
                                  {selectedStudent.evaluations.length} Tests Completed
                                </span>
                              </div>
                            </div>

                            <div className="grid gap-6">
                              {selectedStudent.evaluations.map((evaluation, index) => {
                                const testScore = calculateTestScore(evaluation);
                                const testPerformanceLevel = testScore
                                  ? getPerformanceLevel(testScore.percentage)
                                  : null;

                                return (
                                  <motion.div
                                    key={evaluation.test_id}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: index * 0.1 }}
                                    onClick={() => {
                                      console.log('Selected test_id:', evaluation.test_id);
                                      setSelectedTestId(evaluation.test_id);
                                    }}
                                    className={`group p-6 border-2 rounded-2xl cursor-pointer transition-all duration-300 hover:shadow-lg ${
                                      selectedTestId === evaluation.test_id
                                        ? 'border-[#F59E0B] bg-[#F59E0B]/5 shadow-lg'
                                        : 'border-gray-200 hover:border-[#F59E0B]/50'
                                    }`}>
                                    <div className="flex justify-between items-center">
                                      <div className="flex items-center">
                                        <motion.div
                                          whileHover={{ scale: 1.1 }}
                                          className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 ${
                                            selectedTestId === evaluation.test_id
                                              ? 'bg-[#F59E0B] text-white'
                                              : 'bg-gray-100 text-gray-600 group-hover:bg-[#F59E0B] group-hover:text-white'
                                          } transition-all duration-300`}>
                                          <FaBookOpen />
                                        </motion.div>
                                        <div>
                                          <p className="font-bold text-gray-800 text-lg">
                                            Test ID: {evaluation.test_id}
                                          </p>
                                          <p className="text-gray-600 mb-1">
                                            Exam: {evaluation.overall_feedback.exam}
                                          </p>
                                          <p className="text-sm text-gray-500">
                                            Evaluated:{' '}
                                            {new Date(
                                              evaluation.overall_feedback.evaluation_timestamp
                                            ).toLocaleString('en-IN', {
                                              timeZone: 'Asia/Kolkata',
                                              year: 'numeric',
                                              month: '2-digit',
                                              day: '2-digit',
                                              hour: '2-digit',
                                              minute: '2-digit'
                                            })}
                                          </p>
                                        </div>
                                      </div>

                                      <div className="flex items-center space-x-4">
                                        {/* Individual Test Score */}
                                        {testScore && testPerformanceLevel && (
                                          <motion.div
                                            whileHover={{ scale: 1.05 }}
                                            className={`px-4 py-3 rounded-xl border-2 ${testPerformanceLevel.bgColor} ${testPerformanceLevel.borderColor}`}>
                                            <div className="text-center">
                                              <div
                                                className={`text-xl font-bold ${testPerformanceLevel.color} flex items-center justify-center`}>
                                                <FaTrophy className="mr-1 text-sm" />
                                                {testScore.percentage}%
                                              </div>
                                              <div className="text-xs text-gray-600 mt-1">
                                                {testScore.totalCorrect}/{testScore.totalQuestions}
                                              </div>
                                            </div>
                                          </motion.div>
                                        )}

                                        <motion.div
                                          animate={{
                                            rotate: selectedTestId === evaluation.test_id ? 90 : 0,
                                            scale: selectedTestId === evaluation.test_id ? 1.2 : 1
                                          }}
                                          transition={{ duration: 0.3 }}
                                          className={`${
                                            selectedTestId === evaluation.test_id
                                              ? 'text-[#F59E0B]'
                                              : 'text-gray-400 group-hover:text-[#F59E0B]'
                                          } transition-colors duration-300`}>
                                          <FaChevronRight className="text-xl" />
                                        </motion.div>
                                      </div>
                                    </div>
                                  </motion.div>
                                );
                              })}
                            </div>
                          </motion.div>

                          {/* Enhanced Feedback Section */}
                          <AnimatePresence>
                            {selectedTestId && (
                              <motion.div
                                ref={feedbackRef}
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.5 }}
                                className="space-y-8">
                                {(() => {
                                  const selectedEvaluation = selectedStudent.evaluations.find(
                                    (evaluation) => evaluation.test_id === selectedTestId
                                  );

                                  return selectedEvaluation?.performance_analysis?.length > 0 ? (
                                    <motion.div
                                      initial={{ opacity: 0 }}
                                      animate={{ opacity: 1 }}
                                      transition={{ delay: 0.2 }}
                                      className="space-y-8">
                                      {/* Enhanced Overall Feedback */}
                                      <motion.div
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        className="bg-white border-2 border-gray-200 rounded-2xl p-8 shadow-lg">
                                        <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
                                          <FaChartBar className="text-[#F59E0B] mr-3" />
                                          Overall Performance Analysis
                                          <span className="ml-4 px-3 py-1 bg-[#F59E0B]/10 text-[#F59E0B] rounded-lg text-sm font-medium">
                                            Test ID: {selectedTestId}
                                          </span>
                                        </h3>

                                        <div className="grid md:grid-cols-2 gap-8">
                                          <motion.div
                                            whileHover={{ scale: 1.02 }}
                                            className="p-6 bg-blue-50 border border-blue-200 rounded-2xl">
                                            <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                                              <FaChartLine className="text-blue-500 mr-2" />
                                              Performance Snapshot
                                            </h4>
                                            <p className="text-gray-700 leading-relaxed">
                                              {selectedEvaluation.overall_feedback
                                                .performance_snapshot || 'N/A'}
                                            </p>
                                          </motion.div>

                                          <motion.div
                                            whileHover={{ scale: 1.02 }}
                                            className="p-6 bg-green-50 border border-green-200 rounded-2xl">
                                            <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                                              <FaThumbsUp className="text-green-500 mr-2" />
                                              Conceptual Strengths
                                            </h4>
                                            <ul className="space-y-2">
                                              {selectedEvaluation.overall_feedback.conceptual_strengths?.map(
                                                (strength, index) => (
                                                  <li key={index} className="flex items-start">
                                                    <FaCheckCircle className="text-green-500 mr-2 mt-1 flex-shrink-0" />
                                                    <span className="text-gray-700">
                                                      {strength}
                                                    </span>
                                                  </li>
                                                )
                                              ) || (
                                                <p className="text-gray-500">
                                                  No strengths recorded
                                                </p>
                                              )}
                                            </ul>
                                          </motion.div>

                                          <motion.div
                                            whileHover={{ scale: 1.02 }}
                                            className="p-6 bg-orange-50 border border-orange-200 rounded-2xl">
                                            <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                                              <FaExclamationTriangle className="text-orange-500 mr-2" />
                                              Primary Area for Improvement
                                            </h4>
                                            <p className="text-gray-700 leading-relaxed">
                                              {selectedEvaluation.overall_feedback
                                                .primary_area_for_improvement || 'N/A'}
                                            </p>
                                          </motion.div>

                                          <motion.div
                                            whileHover={{ scale: 1.02 }}
                                            className="p-6 bg-purple-50 border border-purple-200 rounded-2xl">
                                            <h4 className="font-bold text-gray-800 mb-3 flex items-center">
                                              <FaArrowUp className="text-purple-500 mr-2" />
                                              Strategic Action Plan
                                            </h4>
                                            <ul className="space-y-3">
                                              {selectedEvaluation.overall_feedback.strategic_action_plan?.map(
                                                (action, index) => (
                                                  <li
                                                    key={index}
                                                    className="p-3 bg-white rounded-lg border border-purple-200">
                                                    <p className="font-medium text-gray-800">
                                                      {action.action}
                                                    </p>
                                                    <div className="flex items-center space-x-4 mt-2 text-sm">
                                                      <span className="px-2 py-1 bg-purple-100 text-purple-700 rounded">
                                                        Focus: {action.focus}
                                                      </span>
                                                      <span className="px-2 py-1 bg-red-100 text-red-700 rounded">
                                                        Priority: {action.priority}
                                                      </span>
                                                    </div>
                                                  </li>
                                                )
                                              ) || (
                                                <p className="text-gray-500">
                                                  No action plan available
                                                </p>
                                              )}
                                            </ul>
                                          </motion.div>
                                        </div>
                                      </motion.div>

                                      {/* Enhanced Question Analysis */}
                                      <div className="space-y-6">
                                        <h3 className="text-2xl font-bold text-gray-800 flex items-center">
                                          <FaClipboardList className="text-[#F59E0B] mr-3" />
                                          Question-by-Question Analysis
                                        </h3>

                                        {selectedEvaluation.performance_analysis.map(
                                          (evaluation, index) => (
                                            <motion.div
                                              key={index}
                                              initial={{ opacity: 0, y: 20 }}
                                              animate={{ opacity: 1, y: 0 }}
                                              transition={{ delay: index * 0.1 }}
                                              className="bg-white border-2 border-gray-200 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                                              {/* Question Header */}
                                              <div className="p-6 bg-gray-50 border-b border-gray-200">
                                                <div className="flex items-center justify-between">
                                                  <div className="flex items-center">
                                                    <motion.div
                                                      whileHover={{ scale: 1.1, rotate: 5 }}
                                                      className="w-14 h-14 rounded-2xl bg-[#F59E0B] flex items-center justify-center mr-4 shadow-lg">
                                                      <span className="text-white font-bold text-lg">
                                                        {evaluation.question_number}
                                                      </span>
                                                    </motion.div>
                                                    <div>
                                                      <h4 className="text-xl font-bold text-gray-800">
                                                        Question {evaluation.question_number}
                                                      </h4>
                                                      <p className="text-gray-600">
                                                        {evaluation.feedback?.core_concept_tested ||
                                                          'Core concept analysis'}
                                                      </p>
                                                    </div>
                                                  </div>

                                                  <div className="flex items-center space-x-4">
                                                    <motion.span
                                                      whileHover={{ scale: 1.05 }}
                                                      className={`px-4 py-2 rounded-xl text-sm font-bold border-2 ${
                                                        evaluation.is_correct
                                                          ? 'text-green-700 border-green-200 bg-green-50'
                                                          : 'text-red-700 border-red-200 bg-red-50'
                                                      }`}>
                                                      {evaluation.is_correct ? (
                                                        <FaCheck className="inline-block mr-2" />
                                                      ) : (
                                                        <FaTimes className="inline-block mr-2" />
                                                      )}
                                                      {evaluation.is_correct
                                                        ? 'Correct'
                                                        : 'Incorrect'}
                                                    </motion.span>
                                                    <motion.span
                                                      whileHover={{ scale: 1.05 }}
                                                      className="px-4 py-2 rounded-xl text-sm font-bold text-[#F59E0B] border-2 border-[#F59E0B]/30 bg-[#F59E0B]/10">
                                                      {evaluation.marks_awarded ?? 'N/A'} marks
                                                    </motion.span>
                                                  </div>
                                                </div>
                                              </div>

                                              {/* Question Content */}
                                              <div className="p-6">
                                                <div className="grid lg:grid-cols-2 gap-8">
                                                  {/* Left Column - Question Details */}
                                                  <div className="space-y-6">
                                                    <motion.div
                                                      whileHover={{ scale: 1.02 }}
                                                      className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                                                      <div className="flex items-start space-x-3">
                                                        <FaLightbulb className="text-blue-500 mt-1 text-lg" />
                                                        <div>
                                                          <p className="font-bold text-gray-800 mb-2">
                                                            Core Concept
                                                          </p>
                                                          <p className="text-gray-700">
                                                            {evaluation.feedback
                                                              ?.core_concept_tested || 'N/A'}
                                                          </p>
                                                        </div>
                                                      </div>
                                                    </motion.div>

                                                    <motion.div
                                                      whileHover={{ scale: 1.02 }}
                                                      className="p-4 bg-green-50 border border-green-200 rounded-xl">
                                                      <div className="flex items-start space-x-3">
                                                        <FaCheckCircle className="text-green-500 mt-1 text-lg" />
                                                        <div>
                                                          <p className="font-bold text-gray-800 mb-2">
                                                            Correct Answer
                                                          </p>
                                                          <p className="text-gray-700 font-mono bg-white p-2 rounded border">
                                                            {evaluation.expert_calculated_answer ||
                                                              'N/A'}
                                                          </p>
                                                        </div>
                                                      </div>
                                                    </motion.div>

                                                    <motion.div
                                                      whileHover={{ scale: 1.02 }}
                                                      className="p-4 bg-gray-50 border border-gray-200 rounded-xl">
                                                      <div className="flex items-start space-x-3">
                                                        <FaUserEdit className="text-gray-500 mt-1 text-lg" />
                                                        <div>
                                                          <p className="font-bold text-gray-800 mb-2">
                                                            Student Answer
                                                          </p>
                                                          <p className="text-gray-700 font-mono bg-white p-2 rounded border">
                                                            {evaluation.student_final_answer ||
                                                              'N/A'}
                                                          </p>
                                                        </div>
                                                      </div>
                                                    </motion.div>

                                                    {evaluation.error_type && (
                                                      <motion.div
                                                        whileHover={{ scale: 1.02 }}
                                                        className="p-4 bg-red-50 border border-red-200 rounded-xl">
                                                        <div className="flex items-start space-x-3">
                                                          <FaExclamationTriangle className="text-red-500 mt-1 text-lg" />
                                                          <div>
                                                            <p className="font-bold text-gray-800 mb-2">
                                                              Error Type
                                                            </p>
                                                            <p className="text-gray-700">
                                                              {evaluation.error_type}
                                                            </p>
                                                          </div>
                                                        </div>
                                                      </motion.div>
                                                    )}
                                                  </div>

                                                  {/* Right Column - Feedback */}
                                                  <div className="space-y-6">
                                                    {evaluation.feedback?.positive_feedback && (
                                                      <motion.div
                                                        whileHover={{ scale: 1.02 }}
                                                        className="p-6 bg-green-50 border-2 border-green-200 rounded-xl">
                                                        <div className="flex items-start space-x-3">
                                                          <FaThumbsUp className="text-green-500 mt-1 text-lg" />
                                                          <div>
                                                            <p className="font-bold text-gray-800 mb-3">
                                                              Positive Feedback
                                                            </p>
                                                            <p className="text-gray-700 leading-relaxed">
                                                              {
                                                                evaluation.feedback
                                                                  .positive_feedback
                                                              }
                                                            </p>
                                                          </div>
                                                        </div>
                                                      </motion.div>
                                                    )}

                                                    {evaluation.feedback?.error_analysis && (
                                                      <motion.div
                                                        whileHover={{ scale: 1.02 }}
                                                        className="p-6 bg-red-50 border-2 border-red-200 rounded-xl">
                                                        <div className="flex items-start space-x-3">
                                                          <FaSearch className="text-red-500 mt-1 text-lg" />
                                                          <div>
                                                            <p className="font-bold text-gray-800 mb-3">
                                                              Error Analysis
                                                            </p>
                                                            <p className="text-gray-700 leading-relaxed">
                                                              {evaluation.feedback.error_analysis}
                                                            </p>
                                                          </div>
                                                        </div>
                                                      </motion.div>
                                                    )}

                                                    {evaluation.feedback
                                                      ?.improvement_suggestion && (
                                                      <motion.div
                                                        whileHover={{ scale: 1.02 }}
                                                        className="p-6 bg-[#F59E0B]/10 border-2 border-[#F59E0B]/30 rounded-xl">
                                                        <div className="flex items-start space-x-3">
                                                          <FaArrowUp className="text-[#F59E0B] mt-1 text-lg" />
                                                          <div>
                                                            <p className="font-bold text-gray-800 mb-3">
                                                              Improvement Suggestion
                                                            </p>
                                                            <p className="text-gray-700 leading-relaxed">
                                                              {
                                                                evaluation.feedback
                                                                  .improvement_suggestion
                                                              }
                                                            </p>
                                                          </div>
                                                        </div>
                                                      </motion.div>
                                                    )}
                                                  </div>
                                                </div>
                                              </div>
                                            </motion.div>
                                          )
                                        )}
                                      </div>
                                    </motion.div>
                                  ) : (
                                    <motion.div
                                      initial={{ opacity: 0, y: 20 }}
                                      animate={{ opacity: 1, y: 0 }}
                                      className="text-center py-16 bg-white rounded-2xl border-2 border-gray-200">
                                      <FaClipboardList className="text-6xl text-gray-300 mb-6 mx-auto" />
                                      <p className="text-xl text-gray-600 font-medium">
                                        No performance analysis available for this test
                                      </p>
                                    </motion.div>
                                  );
                                })()}
                              </motion.div>
                            )}
                          </AnimatePresence>

                          {!selectedTestId && (
                            <motion.div
                              initial={{ opacity: 0 }}
                              animate={{ opacity: 1 }}
                              className="text-center py-12 bg-gray-50 rounded-2xl border-2 border-dashed border-gray-300">
                              <FaSearch className="text-4xl text-gray-400 mb-4 mx-auto" />
                              <p className="text-lg text-gray-600 font-medium">
                                Select a test above to view detailed feedback and analysis
                              </p>
                            </motion.div>
                          )}
                        </div>
                      ) : (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-center py-16">
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}>
                            <FaClipboardList className="text-8xl text-gray-300 mb-8 mx-auto" />
                          </motion.div>
                          <h3 className="text-2xl font-bold text-gray-700 mb-4">
                            No Evaluations Available
                          </h3>
                          <p className="text-xl text-gray-600 font-medium mb-2">
                            Student ID: {selectedStudentId} hasn't completed any evaluations yet
                          </p>
                          <p className="text-gray-500 max-w-md mx-auto">
                            Performance analytics and detailed feedback will be displayed here once
                            evaluations are completed
                          </p>
                        </motion.div>
                      );
                    })()
                  )}
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default StudentList;
